<template>
  <view class="webview-container">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在加载充值页面...</text>
    </view>

    <!-- WebView -->
    <web-view :src="webviewUrl" @message="handleMessage" @load="handleLoad" @error="handleError"
      class="webview"></web-view>
  </view>
</template>

<script>
import { isLogin, getToken, getUserId } from "@/utils/auth.js";

export default {
  data() {
    return {
      loading: true,
      webviewUrl: "",
      baseUrl: "", // PC端的基础URL
    };
  },
  onLoad(options) {
    // 获取传递的参数
    this.initWebview(options);
  },
  methods: {
    // 初始化webview
    initWebview(options) {
      // 检查登录状态
      if (!isLogin()) {
        uni.showToast({
          title: "请先登录",
          icon: "error",
        });
        setTimeout(() => {
          uni.navigateBack();
        }, 1500);
        return;
      }

      // 设置PC端基础URL (根据环境配置)
      // 开发环境
      if (process.env.NODE_ENV === 'development') {
        this.baseUrl = "http://localhost:9528";
      } else {
        // 生产环境 - 需要替换为实际的PC端域名
        this.baseUrl = "https://your-admin-domain.com";
      }

      // 获取用户token和ID
      const token = getToken();
      const userId = getUserId();

      // 构建webview URL (注意Vue Router的hash模式)
      const page = options.page || "warning"; // 默认显示温馨提示页面
      let routePath = "";

      switch (page) {
        case "warning":
          routePath = "/userCredit/warning";
          break;
        case "credit":
          routePath = "/userCredit";
          break;
        default:
          routePath = "/userCredit/warning";
      }

      // 构建完整的hash路由URL
      let targetUrl = `${this.baseUrl}/#${routePath}`;

      // 添加token和用户ID参数 (小程序兼容方式)
      const params = [];
      params.push(`token=${encodeURIComponent(token)}`);
      params.push(`userId=${encodeURIComponent(userId)}`);
      params.push(`source=miniprogram`); // 标识来源

      this.webviewUrl = `${targetUrl}?${params.join('&')}`;

      console.log("WebView URL:", this.webviewUrl);
    },

    // 处理webview加载完成
    handleLoad(e) {
      console.log("WebView loaded:", e);
      this.loading = false;
    },

    // 处理webview错误
    handleError(e) {
      console.error("WebView error:", e);
      this.loading = false;

      uni.showModal({
        title: "加载失败",
        content: "页面加载失败，请检查网络连接后重试",
        showCancel: true,
        cancelText: "返回",
        confirmText: "重试",
        success: (res) => {
          if (res.confirm) {
            // 重新加载
            this.loading = true;
            this.initWebview({});
          } else {
            // 返回上一页
            uni.navigateBack();
          }
        }
      });
    },

    // 处理来自webview的消息
    handleMessage(e) {
      console.log("Received message from webview:", e.detail.data);

      const data = e.detail.data[0]; // 获取第一个消息
      if (!data) return;

      switch (data.type) {
        case "payment":
          // 处理支付请求
          this.handlePayment(data.paymentData);
          break;
        case "navigation":
          // 处理页面导航
          this.handleNavigation(data);
          break;
        case "close":
          // 关闭webview
          uni.navigateBack();
          break;
        default:
          console.log("Unknown message type:", data.type);
      }
    },

    // 处理支付
    handlePayment(paymentData) {
      console.log("Processing payment:", paymentData);

      if (!paymentData) {
        uni.showToast({
          title: "支付数据错误",
          icon: "error",
        });
        return;
      }

      // 调起微信支付
      uni.requestPayment({
        appId: paymentData.appid,
        timeStamp: paymentData.timestamp,
        nonceStr: paymentData.noncestr,
        package: paymentData.package,
        signType: paymentData.signtype,
        paySign: paymentData.paysign,
        success: (res) => {
          console.log("Payment success:", res);
          uni.showToast({
            title: "支付成功",
            icon: "success",
          });

          // 支付成功后返回首页
          setTimeout(() => {
            uni.switchTab({
              url: "/pages/my/my",
            });
          }, 1500);
        },
        fail: (err) => {
          console.error("Payment failed:", err);

          if (err.errMsg === "requestPayment:fail cancel") {
            uni.showToast({
              title: "您已取消支付",
              icon: "none",
            });
          } else {
            uni.showToast({
              title: "支付失败，请重试",
              icon: "error",
            });
          }
        },
        complete: (res) => {
          console.log("Payment complete:", res);
        }
      });
    },

    // 处理页面导航
    handleNavigation(data) {
      switch (data.action) {
        case "back":
          uni.navigateBack();
          break;
        case "home":
          uni.switchTab({
            url: "/pages/my/my",
          });
          break;
        default:
          console.log("Unknown navigation action:", data.action);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.webview-container {
  width: 100%;
  height: 100vh;
  position: relative;
}

.webview {
  width: 100%;
  height: 100%;
}

.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #00d4aa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.loading-text {
  font-size: 14px;
  color: #666;
  text-align: center;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
